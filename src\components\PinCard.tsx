import GifImage from './GifImage';
import SmartAvatar from './SmartAvatar';
import LazyImage from './LazyImage';
import HighlightText from './HighlightText';
import MixedContent from './MixedContent';
import { formatTime } from '@/utils/dateUtils';
import { memo } from 'react';
import { useRouter } from 'next/navigation';

// 内容元素类型定义
export interface ContentElement {
  type: 'text' | 'image' | 'gif';
  data: {
    text?: string;
    url?: string;
  };
}

export interface PinData {
  id: string;
  content: string; // 保留用于向后兼容和搜索
  contentType: 'text' | 'image' | 'gif' | 'mixed';
  imageUrl?: string; // 保留用于向后兼容
  contentElements: ContentElement[]; // 新增：混合内容元素数组
  timestamp: string;
  setter: {
    name: string;
    avatar: string;
  };
  sender: {
    name: string;
    avatar: string;
  };
  groupName?: string;
}

interface PinCardProps {
  pin: PinData;
  searchQuery?: string;
}

const PinCard = memo(function PinCard({ pin, searchQuery = '' }: PinCardProps) {
  const router = useRouter();

  // 处理发送者点击事件
  const handleSenderClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const encodedUsername = encodeURIComponent(pin.sender.name);
    router.push(`/user/${encodedUsername}`);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all duration-200 overflow-hidden">
      {/* 顶部用户信息栏 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100">
        <div className="flex items-center space-x-3">
          {/* 发送者头像和信息 - 可点击区域 */}
          <div
            className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors duration-200 group"
            onClick={handleSenderClick}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleSenderClick(e as any);
              }
            }}
            aria-label={`查看 ${pin.sender.name} 的所有消息`}
          >
            {/* 发送者头像 */}
            <SmartAvatar
              nickname={pin.sender.name}
              size={32}
              className="flex-shrink-0 group-hover:ring-2 group-hover:ring-blue-200 transition-all duration-200"
            />

            {/* 用户信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <HighlightText
                  text={pin.sender.name}
                  searchQuery={searchQuery}
                  className="font-medium text-gray-900 text-sm truncate group-hover:text-blue-600 transition-colors duration-200"
                  highlightClassName="bg-blue-200 text-blue-900 px-0.5 rounded font-medium"
                />
                <span className="text-xs text-gray-500">
                  发送
                </span>
              </div>
              <div className="flex items-center space-x-2 mt-0.5">
                <span className="text-xs text-gray-500">
                  {formatTime(pin.timestamp)}
                </span>
                <span className="text-xs text-gray-400">
                  由
                </span>
                <HighlightText
                  text={pin.setter.name}
                  searchQuery={searchQuery}
                  className="text-xs text-blue-600 font-medium"
                  highlightClassName="bg-blue-200 text-blue-900 px-0.5 rounded font-medium"
                />
                <span className="text-xs text-gray-400">
                  设为精华
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-3">
        {pin.contentType === 'mixed' ? (
          /* 混合内容 */
          <MixedContent
            contentElements={pin.contentElements}
            searchQuery={searchQuery}
          />
        ) : (
          /* 单一类型内容（向后兼容） */
          <>
            {/* 文本内容 */}
            {pin.content && pin.contentType === 'text' && (
              <div className="mb-3">
                <HighlightText
                  text={pin.content}
                  searchQuery={searchQuery}
                  className="text-gray-900 text-sm leading-relaxed break-words block"
                  highlightClassName="bg-yellow-200 text-yellow-900 px-0.5 rounded"
                />
              </div>
            )}

            {/* 图片/动图内容 */}
            {(pin.contentType === 'image' || pin.contentType === 'gif') && pin.imageUrl ? (
              <div className="rounded-lg overflow-hidden">
                {pin.contentType === 'gif' ? (
                  <GifImage
                    src={pin.imageUrl}
                    alt="精华动图"
                    className="w-full"
                  />
                ) : (
                  <LazyImage
                    src={pin.imageUrl}
                    alt="精华内容"
                    width={400}
                    height={300}
                    className="w-full h-auto object-cover"
                  />
                )}
              </div>
            ) : null}
          </>
        )}
      </div>
    </div>
  );
});

export default PinCard;
