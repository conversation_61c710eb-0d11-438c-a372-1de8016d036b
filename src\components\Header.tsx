'use client';

import { useState } from 'react';
import Link from 'next/link';
import SortSelector from './SortSelector';
import FilterSelector from './FilterSelector';

interface HeaderProps {
  onSearch?: (query: string) => void;
  onSortChange?: (sort: string) => void;
  onFilterChange?: (filter: string) => void;
  sortLoading?: boolean;
}

export default function Header({
  onSearch,
  onSortChange,
  onFilterChange,
  sortLoading = false
}: HeaderProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('latest');
  const [filterBy, setFilterBy] = useState('all');

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    onSortChange?.(value);
  };

  const handleFilterChange = (value: string) => {
    setFilterBy(value);
    onFilterChange?.(value);
  };

  return (
    <header className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14">
          {/* Logo和标题 */}
          <div className="flex items-center space-x-4">
            <Link href="/" className="hover:opacity-80 transition-opacity duration-200">
              <h1 className="text-xl font-bold text-gray-900 cursor-pointer">
                Q精华 Hub
              </h1>
            </Link>
          </div>

          {/* 功能区域 */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* 搜索框 */}
            <form onSubmit={handleSearchSubmit} className="hidden sm:block">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="搜索精华内容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-48 lg:w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm transition-all duration-200"
                />
              </div>
            </form>

            {/* 排序选择器 */}
            <SortSelector
              value={sortBy}
              onChange={handleSortChange}
              loading={sortLoading}
            />

            {/* 筛选器 */}
            <FilterSelector
              value={filterBy}
              onChange={handleFilterChange}
            />


          </div>
        </div>

        {/* 移动端搜索框和控制器 */}
        <div className="sm:hidden pb-4 space-y-3">
          {/* 搜索框 */}
          <form onSubmit={handleSearchSubmit}>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="搜索精华内容..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
          </form>

          {/* 移动端排序和筛选 */}
          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <SortSelector
                value={sortBy}
                onChange={handleSortChange}
                loading={sortLoading}
                className="w-full"
              />
            </div>
            <div className="flex-1">
              <FilterSelector
                value={filterBy}
                onChange={handleFilterChange}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
