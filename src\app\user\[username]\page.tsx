'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import MasonryGrid from '@/components/MasonryGrid';
import { PinData } from '@/components/PinCard';
import { loadPinsFromJson } from '@/services/dataService';
import { loadAvatarConfig } from '@/services/avatarService';
import { useSortedPins } from '@/hooks/useSortedPins';
import { useDebounce } from '@/hooks/useDebounce';
import { useAvatarPreload } from '@/hooks/useAvatarPreload';
import { SortType } from '@/utils/sortUtils';
import Breadcrumb from '@/components/Breadcrumb';
import UserInfoCard from '@/components/UserInfoCard';

export default function UserPage() {
  const params = useParams();
  const router = useRouter();
  const username = decodeURIComponent(params.username as string);
  
  const [pins, setPins] = useState<PinData[]>([]);
  const [userPins, setUserPins] = useState<PinData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortType>('latest');
  const [filterBy, setFilterBy] = useState('all');
  const [sortLoading, setSortLoading] = useState(false);

  // 使用防抖优化搜索性能
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // 使用优化后的排序Hook
  const { sortedPins } = useSortedPins(
    userPins,
    debouncedSearchQuery,
    filterBy,
    sortBy
  );

  // 使用头像预加载Hook
  useAvatarPreload(userPins);

  // 初始化加载数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 并行加载精华消息数据和头像配置
        const [loadedPins] = await Promise.all([
          loadPinsFromJson(),
          loadAvatarConfig()
        ]);

        setPins(loadedPins);

        // 筛选该用户的消息
        const filteredPins = loadedPins.filter(pin => pin.sender.name === username);
        setUserPins(filteredPins);

        if (filteredPins.length === 0) {
          setError(`未找到用户 "${username}" 的消息`);
        }

      } catch (err) {
        console.error('数据加载失败:', err);
        setError(err instanceof Error ? err.message : '数据加载失败');
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, [username]);

  // 搜索功能
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // 排序功能
  const handleSortChange = (sort: string) => {
    setSortLoading(true);
    setSortBy(sort as SortType);
    setTimeout(() => setSortLoading(false), 100);
  };

  // 筛选功能
  const handleFilterChange = (filter: string) => {
    setFilterBy(filter);
  };

  // 返回首页
  const handleBackToHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        onSearch={handleSearch}
        onSortChange={handleSortChange}
        onFilterChange={handleFilterChange}
        sortLoading={sortLoading}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 面包屑导航 */}
        <Breadcrumb
          items={[
            { label: '首页', href: '/' },
            { label: `${username} 的消息`, current: true }
          ]}
          className="mb-6"
        />

        {/* 用户信息头部 */}
        <UserInfoCard
          username={username}
          userPins={userPins}
          onBackToHome={handleBackToHome}
        />

        {/* 内容区域 */}
        {loading ? (
          <div className="flex justify-center items-center py-16">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <div className="text-gray-600">正在加载用户消息...</div>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-16">
            <div className="text-gray-500 text-lg mb-4">{error}</div>
            <button
              onClick={handleBackToHome}
              className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
            >
              返回首页
            </button>
          </div>
        ) : (
          <MasonryGrid
            pins={sortedPins}
            hasMore={false}
            loading={sortLoading}
            searchQuery={debouncedSearchQuery}
          />
        )}
      </main>
    </div>
  );
}
